{"cells": [{"cell_type": "markdown", "metadata": {"id": "ofTDOzMx0CKv"}, "source": ["🚀 我的环境：\n", "\n", "- 语言环境：Python3.6.5\n", "- 编译器：jupyter notebook\n", "- 深度学习环境：TensorFlow2.4.1\n", "- 显卡（GPU）：NVIDIA GeForce RTX 3080"]}, {"cell_type": "markdown", "metadata": {"tags": [], "id": "nTwIv5_N0CKw"}, "source": ["# 一、前期工作"]}, {"cell_type": "markdown", "metadata": {"id": "oPXkWMWu0CKw"}, "source": ["## 1. 设置GPU"]}, {"cell_type": "markdown", "metadata": {"id": "n_Z2biIq0CKw"}, "source": ["如果使用的是CPU可以忽略这步"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "WZ9nesxJ0CKx"}, "outputs": [], "source": ["import tensorflow as tf\n", "gpus = tf.config.list_physical_devices(\"GPU\")\n", "\n", "if gpus:\n", "    gpu0 = gpus[0] #如果有多个GPU，仅使用第0个GPU\n", "    tf.config.experimental.set_memory_growth(gpu0, True) #设置GPU显存用量按需使用\n", "    tf.config.set_visible_devices([gpu0],\"GPU\")"]}, {"cell_type": "markdown", "metadata": {"id": "xu7NRHEn0CKx"}, "source": ["## 2. 导入数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "x2bi_3470CKx", "outputId": "0c1c7913-5c13-4b63-fb8b-6c45a27dd866"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Downloading data from https://www.cs.toronto.edu/~kriz/cifar-10-python.tar.gz\n", "\u001b[1m170498071/170498071\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m4s\u001b[0m 0us/step\n"]}], "source": ["import tensorflow as tf\n", "from tensorflow.keras import datasets, layers, models\n", "import matplotlib.pyplot as plt\n", "\n", "(train_images, train_labels), (test_images, test_labels) = datasets.cifar10.load_data()"]}, {"cell_type": "markdown", "metadata": {"id": "MGG0BrPh0CKy"}, "source": ["## 3. 归一化"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "_q7tHr2V0CKy", "outputId": "82251f31-3c78-4d03-c674-da00f61813af"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["((50000, 32, 32, 3), (10000, 32, 32, 3), (50000, 1), (10000, 1))"]}, "metadata": {}, "execution_count": 3}], "source": ["# 将像素的值标准化至0到1的区间内。\n", "train_images, test_images = train_images / 255.0, test_images / 255.0\n", "\n", "train_images.shape,test_images.shape,train_labels.shape,test_labels.shape"]}, {"cell_type": "markdown", "metadata": {"id": "AtVy_FAe0CKz"}, "source": ["## 4. 可视化"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 249}, "id": "uli2g_T60CKz", "outputId": "33a61967-0859-4153-f372-2d436b77a605"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 2000x1000 with 20 Axes>"], "image/png": "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\n"}, "metadata": {}}], "source": ["class_names = ['airplane', 'automobile', 'bird', 'cat', 'deer','dog', 'frog', 'horse', 'ship', 'truck']\n", "\n", "plt.figure(figsize=(20,10))\n", "for i in range(20):\n", "    plt.subplot(5,10,i+1)\n", "    plt.xticks([])\n", "    plt.yticks([])\n", "    plt.grid(False)\n", "    plt.imshow(train_images[i], cmap=plt.cm.binary)\n", "    plt.xlabel(class_names[train_labels[i][0]])\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"id": "L_1VVkXB0CKz"}, "source": ["# 二、构建CNN网络"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 440}, "id": "HgEUp6q20CKz", "outputId": "75b52be1-0579-4360-fdaa-421485f34d6d"}, "outputs": [{"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/keras/src/layers/convolutional/base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1mModel: \"sequential\"\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"sequential\"</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape          \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m      Param #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d (\u001b[38;5;33mConv2D\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m30\u001b[0m, \u001b[38;5;34m30\u001b[0m, \u001b[38;5;34m32\u001b[0m)     │           \u001b[38;5;34m896\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d (\u001b[38;5;33mMaxPooling2D\u001b[0m)    │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m15\u001b[0m, \u001b[38;5;34m15\u001b[0m, \u001b[38;5;34m32\u001b[0m)     │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_1 (\u001b[38;5;33mConv2D\u001b[0m)               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m13\u001b[0m, \u001b[38;5;34m13\u001b[0m, \u001b[38;5;34m64\u001b[0m)     │        \u001b[38;5;34m18,496\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_1 (\u001b[38;5;33mMaxPooling2D\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m6\u001b[0m, \u001b[38;5;34m6\u001b[0m, \u001b[38;5;34m64\u001b[0m)       │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_2 (\u001b[38;5;33mConv2D\u001b[0m)               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m4\u001b[0m, \u001b[38;5;34m4\u001b[0m, \u001b[38;5;34m64\u001b[0m)       │        \u001b[38;5;34m36,928\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten (\u001b[38;5;33m<PERSON><PERSON><PERSON>\u001b[0m)               │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m1024\u001b[0m)           │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (\u001b[38;5;33mDense\u001b[0m)                   │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m64\u001b[0m)             │        \u001b[38;5;34m65,600\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (\u001b[38;5;33mDense\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m10\u001b[0m)             │           \u001b[38;5;34m650\u001b[0m │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                    </span>┃<span style=\"font-weight: bold\"> Output Shape           </span>┃<span style=\"font-weight: bold\">       Param # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">30</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">30</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)     │           <span style=\"color: #00af00; text-decoration-color: #00af00\">896</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)    │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">15</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">15</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)     │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">13</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">13</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)     │        <span style=\"color: #00af00; text-decoration-color: #00af00\">18,496</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">6</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">6</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)       │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_2 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">4</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">4</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)       │        <span style=\"color: #00af00; text-decoration-color: #00af00\">36,928</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Flatten</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">1024</span>)           │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                   │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)             │        <span style=\"color: #00af00; text-decoration-color: #00af00\">65,600</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">10</span>)             │           <span style=\"color: #00af00; text-decoration-color: #00af00\">650</span> │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m122,570\u001b[0m (478.79 KB)\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">122,570</span> (478.79 KB)\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m122,570\u001b[0m (478.79 KB)\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">122,570</span> (478.79 KB)\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"]}, "metadata": {}}], "source": ["model = models.Sequential([\n", "    layers.Conv2D(32, (3, 3), activation='relu', input_shape=(32, 32, 3)), #卷积层1，卷积核3*3\n", "    layers.MaxPooling2D((2, 2)),                   #池化层1，2*2采样\n", "    layers.Conv2D(64, (3, 3), activation='relu'),  #卷积层2，卷积核3*3\n", "    layers.MaxPooling2D((2, 2)),                   #池化层2，2*2采样\n", "    layers.Conv2D(64, (3, 3), activation='relu'),  #卷积层3，卷积核3*3\n", "\n", "    layers.Flatten(),                      #Flatten层，连接卷积层与全连接层\n", "    layers.Den<PERSON>(64, activation='relu'),   #全连接层，特征进一步提取\n", "    layers.Den<PERSON>(10)                       #输出层，输出预期结果\n", "])\n", "\n", "model.summary()  # 打印网络结构"]}, {"cell_type": "markdown", "metadata": {"id": "FxtQZMjI0CKz"}, "source": ["# 三、编译"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "rinq7Fi00CK0"}, "outputs": [], "source": ["model.compile(optimizer='adam',\n", "              loss=tf.keras.losses.SparseCategoricalCrossentropy(from_logits=True),\n", "              metrics=['accuracy'])"]}, {"cell_type": "markdown", "metadata": {"id": "uBJWQ6Nh0CK0"}, "source": ["# 四、训练模型"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"scrolled": true, "colab": {"base_uri": "https://localhost:8080/"}, "id": "H8fA-XZ90CK0", "outputId": "a4983e4a-8a78-422f-9d5a-0c2e58602d97"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Epoch 1/10\n", "\u001b[1m1563/1563\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m13s\u001b[0m 6ms/step - accuracy: 0.3247 - loss: 1.8105 - val_accuracy: 0.5177 - val_loss: 1.3162\n", "Epoch 2/10\n", "\u001b[1m1563/1563\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m15s\u001b[0m 4ms/step - accuracy: 0.5585 - loss: 1.2354 - val_accuracy: 0.6244 - val_loss: 1.0682\n", "Epoch 3/10\n", "\u001b[1m1563/1563\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m6s\u001b[0m 4ms/step - accuracy: 0.6360 - loss: 1.0322 - val_accuracy: 0.6341 - val_loss: 1.0256\n", "Epoch 4/10\n", "\u001b[1m1563/1563\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m6s\u001b[0m 4ms/step - accuracy: 0.6703 - loss: 0.9322 - val_accuracy: 0.6828 - val_loss: 0.9122\n", "Epoch 5/10\n", "\u001b[1m1563/1563\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m11s\u001b[0m 4ms/step - accuracy: 0.7002 - loss: 0.8487 - val_accuracy: 0.6833 - val_loss: 0.9034\n", "Epoch 6/10\n", "\u001b[1m1563/1563\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m10s\u001b[0m 4ms/step - accuracy: 0.7192 - loss: 0.7937 - val_accuracy: 0.6998 - val_loss: 0.8507\n", "Epoch 7/10\n", "\u001b[1m1563/1563\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m6s\u001b[0m 4ms/step - accuracy: 0.7362 - loss: 0.7516 - val_accuracy: 0.6924 - val_loss: 0.8959\n", "Epoch 8/10\n", "\u001b[1m1563/1563\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m6s\u001b[0m 4ms/step - accuracy: 0.7529 - loss: 0.6970 - val_accuracy: 0.7083 - val_loss: 0.8542\n", "Epoch 9/10\n", "\u001b[1m1563/1563\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m6s\u001b[0m 4ms/step - accuracy: 0.7689 - loss: 0.6578 - val_accuracy: 0.7171 - val_loss: 0.8279\n", "Epoch 10/10\n", "\u001b[1m1563/1563\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m10s\u001b[0m 4ms/step - accuracy: 0.7811 - loss: 0.6272 - val_accuracy: 0.7067 - val_loss: 0.8678\n"]}], "source": ["history = model.fit(train_images, train_labels, epochs=10,\n", "                    validation_data=(test_images, test_labels))"]}, {"cell_type": "markdown", "metadata": {"id": "zD6FiQG00CK0"}, "source": ["# 五、预测"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 447}, "id": "-u-4gXf20CK0", "outputId": "f82ed1b4-8f98-44bc-cd1b-b310b1f33447"}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["<matplotlib.image.AxesImage at 0x786afa3413d0>"]}, "metadata": {}, "execution_count": 8}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAAaAAAAGdCAYAAABU0qcqAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjAsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvlHJYcgAAAAlwSFlzAAAPYQAAD2EBqD+naQAALaJJREFUeJzt3X9wVfWd//HXuTf33vy+IQn5ZQINoKAi7MoqzdiyVFh+7HxdrMyOtp3vYtevjm50VtluW3Zare7uxNoZa9uh+Me6sv1O0a47RUdnilUscdoFW6gUtW2+QqNASYKAyQ35cXNz7/n+YUk3Cvp5Q8InCc/HzJ2B3Hfe+Zx77rnve3JvXjcIwzAUAADnWcT3AgAAFyYGEADACwYQAMALBhAAwAsGEADACwYQAMALBhAAwAsGEADAizzfC3i/XC6nI0eOqKSkREEQ+F4OAMAoDEP19vaqrq5OkciZz3Mm3AA6cuSIGhoafC8DAHCODh06pPr6+jNeP24DaOPGjfrGN76hzs5OLVy4UN/5znd09dVXf+T3lZSUSJJ+9atfjfz7owwPDzuvi7Oq8++CuM2tgVbGekt5aPzFemjoHrE3dxfkTK0DQ30o230wML46MVESzcbzWLNsY29vr6688sqPfAwflwH0gx/8QOvXr9ejjz6qxYsX65FHHtHKlSvV1tamqqqqD/**********************************************************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\n"}, "metadata": {}}], "source": ["plt.imshow(test_images[1])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-U5aKvPP0CK0", "outputId": "daae7bae-cb2e-4bba-847e-8e9f8976f845"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\u001b[1m313/313\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 3ms/step\n", "ship\n"]}], "source": ["import numpy as np\n", "\n", "pre = model.predict(test_images)\n", "print(class_names[np.argmax(pre[1])])"]}, {"cell_type": "markdown", "metadata": {"id": "bhwu9lEX0CK0"}, "source": ["# 六、模型评估"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 472}, "id": "QQZgAe4N0CK0", "outputId": "f2fb9f03-d867-4691-ae33-59019f3f1c48"}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 640x480 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["313/313 - 1s - 2ms/step - accuracy: 0.7067 - loss: 0.8678\n"]}], "source": ["import matplotlib.pyplot as plt\n", "\n", "plt.plot(history.history['accuracy'], label='accuracy')\n", "plt.plot(history.history['val_accuracy'], label = 'val_accuracy')\n", "plt.xlabel('Epoch')\n", "plt.ylabel('Accuracy')\n", "plt.ylim([0.5, 1])\n", "plt.legend(loc='lower right')\n", "plt.show()\n", "\n", "test_loss, test_acc = model.evaluate(test_images,  test_labels, verbose=2)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "aHyMM6OW0CK1", "outputId": "071ae9dc-f240-41c3-a06c-d57aad9e9283"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["0.7067000269889832\n"]}], "source": ["print(test_acc)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}, "toc": {"base_numbering": 1, "nav_menu": {}, "number_sections": false, "sideBar": true, "skip_h1_title": false, "title_cell": "Table of Contents", "title_sidebar": "Contents", "toc_cell": false, "toc_position": {"height": "calc(100% - 180px)", "left": "10px", "top": "150px", "width": "165px"}, "toc_section_display": true, "toc_window_display": true}, "colab": {"provenance": [], "toc_visible": true, "gpuType": "T4"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}