import tensorflow as tf

# 自动管理GPU显存，避免一次性占满
gpus = tf.config.list_physical_devices('GPU')
if gpus:
    try:
        for gpu in gpus:
            tf.config.experimental.set_memory_growth(gpu,  True)
        print("[INFO] GPU 显存按需增长已启用")
    except RuntimeError as e:
        print("[ERROR] GPU 设置失败:", e)

from tensorflow.keras.datasets import cifar10
from tensorflow.keras.utils import to_categorical

# 加载数据集
(x_train, y_train), (x_test, y_test) = cifar10.load_data()

# 查看原始数据形状
print("训练集形状:", x_train.shape)
print("测试集形状:", x_test.shape)


# 设置模型参数
IMG_SIZE = 224  # ResNet50 需要更大尺寸
BATCH_SIZE = 512
NUM_CLASSES = 10
EPOCHS = 10 # 训练轮数

# 独热编码标签
# 独热编码的作用是将分类标签转换为神经网络更易处理的格式
y_train = to_categorical(y_train, NUM_CLASSES)
y_test = to_categorical(y_test, NUM_CLASSES)


from tensorflow.keras.applications.resnet50 import preprocess_input
from tensorflow.data.experimental import AUTOTUNE
import tensorflow as tf

# 图像预处理函数
def preprocess(image, label):
    image = tf.image.resize(image, [IMG_SIZE, IMG_SIZE])
    image = preprocess_input(image)  # 标准化为 ResNet50 所需格式
    return image, label


# 构建训练集数据流
# 1. 从特征数据x_train和标签数据y_train创建数据集
#    每个元素是一个(特征, 标签)的元组，对应一个样本
train_ds = tf.data.Dataset.from_tensor_slices((x_train, y_train))

# 2. 打乱数据顺序并应用预处理
#    shuffle(10000)：使用大小为10000的缓冲区打乱数据，确保训练随机性
#    map(...)：对每个样本应用preprocess预处理函数
#    num_parallel_calls=AUTOTUNE：自动根据CPU核心数并行处理，提高效率
train_ds = train_ds.shuffle(10000).map(preprocess, num_parallel_calls=AUTOTUNE)

# 3. 分批次并设置预加载
#    batch(BATCH_SIZE)：将数据分成批次，每批包含BATCH_SIZE个样本
#    prefetch(AUTOTUNE)：在模型处理当前批次时，异步加载下一批数据
train_ds = train_ds.batch(BATCH_SIZE).prefetch(AUTOTUNE)

# 构建测试集数据流
# 1. 从测试集特征x_test和标签y_test创建数据集
test_ds = tf.data.Dataset.from_tensor_slices((x_test, y_test))

# 2. 应用预处理（与训练集使用相同的预处理逻辑）
#    测试集不需要shuffle，保持原始顺序不影响评估结果
test_ds = test_ds.map(preprocess, num_parallel_calls=AUTOTUNE)

# 3. 分批次并预加载（与训练集处理一致）
test_ds = test_ds.batch(BATCH_SIZE).prefetch(AUTOTUNE)

from tensorflow.keras.applications import ResNet50
from tensorflow.keras.layers import GlobalAveragePooling2D, Dense, Input, BatchNormalization
from tensorflow.keras.models import Model

# 加载不包含顶部全连接层的 ResNet50
base_model = ResNet50(include_top=False, weights=None, input_shape=(IMG_SIZE, IMG_SIZE, 3))
base_model.load_weights('/resnet50_weights_tf_dim_ordering_tf_kernels_notop.h5')  # 请确保该文件存在
base_model.trainable = False  # 冻结主干网络参数

# 构建完整模型
inputs = Input(shape=(IMG_SIZE, IMG_SIZE, 3))
x = base_model(inputs, training=False)
x = GlobalAveragePooling2D()(x)
x = BatchNormalization()(x)
outputs = Dense(NUM_CLASSES, activation='softmax')(x)

model = Model(inputs, outputs)
model.summary()

from tensorflow.keras.optimizers import Adam

# 编译模型
model.compile(
    optimizer=Adam(learning_rate=1e-4, clipnorm=1.0),
    loss='categorical_crossentropy',
    metrics=['accuracy']
)


# 模型训练
history = model.fit(
    train_ds,
    epochs=EPOCHS,
    validation_data=test_ds,
    verbose=2  # 每轮训练输出一行日志
)


# 测试集上评估
loss, acc = model.evaluate(test_ds)
print(f"[测试集准确率]: {acc:.4f}")


# 保存模型
MODEL_SAVE_PATH = 'cifar10_resnet50.h5'
model.save(MODEL_SAVE_PATH)
print(f"[INFO] 模型已保存到 {MODEL_SAVE_PATH}")


import matplotlib.pyplot as plt

# 绘制训练/验证准确率（英文显示）
plt.figure(figsize=(10,4))
plt.plot(history.history['accuracy'], label='Training Accuracy')
plt.plot(history.history['val_accuracy'], label='Validation Accuracy')
plt.title('Model Accuracy')
plt.xlabel('Epoch')
plt.ylabel('Accuracy')
plt.legend()
plt.grid(True)
plt.show()

# 绘制训练/验证损失（英文显示）
plt.figure(figsize=(10,4))
plt.plot(history.history['loss'], label='Training Loss')
plt.plot(history.history['val_loss'], label='Validation Loss')
plt.title('Model Loss')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.legend()
plt.grid(True)
plt.show()

import tensorflow as tf
from tensorflow.keras.models import load_model
from tensorflow.keras.preprocessing import image
from tensorflow.keras.applications.resnet50 import preprocess_input
import matplotlib.pyplot as plt
import numpy as np
import os

# 图像尺寸
IMG_SIZE = 224

# 模型路径（请确保该路径下模型已保存）
MODEL_PATH = 'cifar10_resnet50.h5'

# 测试图片路径
TEST_DIR = './test'

# CIFAR-10 分类标签
CLASS_NAMES = ['airplane', 'automobile', 'bird', 'cat', 'deer',
               'dog', 'frog', 'horse', 'ship', 'truck']


print("[INFO] 正在加载训练好的模型...")
model = load_model(MODEL_PATH)
print("模型加载成功！")

def visualize_prediction(img_path, model):
    # 加载图片并调整大小
    img = image.load_img(img_path, target_size=(IMG_SIZE, IMG_SIZE))
    img_array = image.img_to_array(img)

    # 原始图像（0~1归一化）
    original_img = img_array.copy() / 255.0

    # 模型所需预处理
    processed_img = preprocess_input(img_array.copy())

    # 模型预测
    predictions = model.predict(np.expand_dims(processed_img, axis=0))[0]
    top_class = CLASS_NAMES[np.argmax(predictions)]

    # 可视化三个部分
    plt.figure(figsize=(18, 6))

    # 原始图像
    plt.subplot(1, 3, 1)
    plt.imshow(original_img)
    plt.title(f"Original Image\n{os.path.basename(img_path)}", pad=20)
    plt.axis('off')

    # 预处理图像（标准化后图像可视化处理）
    plt.subplot(1, 3, 2)
    processed_display = processed_img - processed_img.min()
    processed_display /= processed_display.max()
    plt.imshow(processed_display)
    plt.title("Preprocessed Image", pad=20)
    plt.axis('off')

    # 概率分布柱状图
    plt.subplot(1, 3, 3)
    bars = plt.barh(CLASS_NAMES, predictions, color='skyblue')
    plt.xlabel('Probability', labelpad=10)
    plt.title('Prediction Probabilities', pad=20)
    plt.xlim([0, 1])

    # 高亮最高类别
    max_idx = np.argmax(predictions)
    bars[max_idx].set_color('orange')
    plt.annotate(f'{predictions[max_idx]:.2%}',
                 xy=(predictions[max_idx], max_idx),
                 xytext=(5, 0), textcoords='offset points',
                 ha='left', va='center', fontsize=10, color='red')

    plt.tight_layout()
    plt.show()

    # 输出预测结果
    print(f"\n预测结果: {img_path}")
    print(f"最可能类别: {top_class} ({predictions.max():.2%})")
    print("所有类别概率:")
    for name, prob in zip(CLASS_NAMES, predictions):
        print(f"  - {name.ljust(10)}: {prob:.2%}")

print(f"[INFO] 正在处理测试目录: {TEST_DIR}")
for img_file in sorted(os.listdir(TEST_DIR)):
    if img_file.lower().endswith(('.png', '.jpg', '.jpeg')):
        img_path = os.path.join(TEST_DIR, img_file)
        print("\n" + "="*50)
        visualize_prediction(img_path, model)